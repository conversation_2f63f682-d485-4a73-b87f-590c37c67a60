"use client";

import React, { useState, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Search,
  Filter,
  Plus,
  Calculator,
  Send,
  CheckCircle,
  XCircle,
  Clock,
  DollarSign,
  Users,
  TrendingUp,
  AlertTriangle,
  Eye,
  CreditCard,
  Banknote,
  FileText,
  Settings,
  RefreshCw,
  Download,
  Upload,
  Target,
  Percent,
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { toast } from "sonner";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

// Import our payout actions
import {
  calculatePayoutAmountAction,
  processDriverPayoutAction,
  approveDriverPayoutAction,
  rejectDriverPayoutAction,
  getAssignmentPayoutsAction,
  getPayoutsSummaryAction,
  processBulkPayoutsAction,
  bulkApprovePayoutsAction,
  refreshPayoutsAction,
  getPayoutStatsAction,
} from "@/actions/admin/payouts";

// Import types
import { PayoutRecord } from "@/types/payment-contract";

interface Assignment {
  id: string;
  driverName: string;
  vehicleName: string;
  vehicleRegistration: string;
  weeklyRate: number;
  outstandingBalance: number;
}

interface DriverPayoutManagementProps {
  assignments: Assignment[];
  onRefresh?: () => void;
}

export default function DriverPayoutManagement({
  assignments,
  onRefresh,
}: DriverPayoutManagementProps) {
  const [payouts, setPayouts] = useState<PayoutRecord[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [filterStatus, setFilterStatus] = useState<string>("all");
  const [selectedWeek, setSelectedWeek] = useState("");

  // Dialog states
  const [showCalculateDialog, setShowCalculateDialog] = useState(false);
  const [showProcessDialog, setShowProcessDialog] = useState(false);
  const [showBulkDialog, setShowBulkDialog] = useState(false);
  const [showStatsDialog, setShowStatsDialog] = useState(false);
  const [showApprovalDialog, setShowApprovalDialog] = useState(false);
  const [selectedPayout, setSelectedPayout] = useState<PayoutRecord | null>(null);

  // Form states
  const [calculateFormData, setCalculateFormData] = useState({
    assignmentId: "",
    weekStart: "",
    weekEnd: "",
    notes: "",
  });

  const [processFormData, setProcessFormData] = useState({
    assignmentId: "",
    weekStart: "",
    weekEnd: "",
    payoutAmount: "",
    notes: "",
  });

  const [bulkFormData, setBulkFormData] = useState({
    weekStart: "",
    weekEnd: "",
    assignmentPayouts: [] as Array<{ assignmentId: string; payoutAmount: string; notes: string }>,
  });

  const [approvalData, setApprovalData] = useState({
    action: "approve" as "approve" | "reject",
    notes: "",
  });

  // Stats state
  const [payoutStats, setPayoutStats] = useState({
    totalPayouts: "0",
    pendingAmount: "0",
    approvedAmount: "0",
    rejectedAmount: "0",
    totalDrivers: 0,
    pendingCount: 0,
    approvedCount: 0,
    rejectedCount: 0,
    averagePayout: "0",
    highestPayout: "0",
  });

  const [payoutData, setPayoutData] = useState({
    summary: [] as any[],
    totalRecords: 0,
    processingRate: "0",
    approvalRate: "0",
  });

  // Load payout data
  useEffect(() => {
    loadPayoutData();
    loadPayoutStats();
  }, [assignments]);

  const loadPayoutData = async () => {
    if (assignments.length === 0) return;

    setLoading(true);
    try {
      const assignmentIds = assignments.map(a => parseInt(a.id));
      
      const [summaryResult] = await Promise.all([
        getPayoutsSummaryAction(assignmentIds),
      ]);

      if (summaryResult.success && summaryResult.data) {
        setPayoutData({
          summary: summaryResult.data,
          totalRecords: summaryResult.data.length,
          processingRate: (summaryResult.data.filter(p => p.payoutCount > 0).length / summaryResult.data.length * 100).toFixed(1),
          approvalRate: (summaryResult.data.filter(p => p.approvedCount > 0).length / summaryResult.data.length * 100).toFixed(1),
        });
      }

      // Load detailed payouts for all assignments
      const allPayouts: PayoutRecord[] = [];
      for (const assignmentId of assignmentIds) {
        const result = await getAssignmentPayoutsAction(assignmentId);
        if (result.success && result.data) {
          allPayouts.push(...result.data);
        }
      }
      
      setPayouts(allPayouts);
    } catch (error) {
      console.error("Error loading payout data:", error);
      toast.error("Failed to load payout data");
    } finally {
      setLoading(false);
    }
  };

  const loadPayoutStats = async () => {
    if (assignments.length === 0) return;

    try {
      const assignmentIds = assignments.map(a => parseInt(a.id));
      const result = await getPayoutStatsAction(assignmentIds);
      
      if (result.success && result.data) {
        setPayoutStats({
          totalPayouts: result.data.totalPayouts,
          pendingAmount: "0", // Not available in current stats
          approvedAmount: "0", // Not available in current stats
          rejectedAmount: "0", // Not available in current stats
          totalDrivers: assignments.length, // Use assignments count
          pendingCount: result.data.pendingApprovalCount,
          approvedCount: result.data.approvedCount,
          rejectedCount: result.data.rejectedCount,
          averagePayout: result.data.averagePayoutAmount,
          highestPayout: "0", // Not available in current stats
        });
      }
    } catch (error) {
      console.error("Error loading payout stats:", error);
    }
  };

  const handleCalculatePayout = async () => {
    if (!calculateFormData.assignmentId || !calculateFormData.weekStart || !calculateFormData.weekEnd) {
      toast.error("Please fill in all required fields");
      return;
    }

    try {
      const result = await calculatePayoutAmountAction(
        Number(calculateFormData.assignmentId),
        calculateFormData.weekStart,
        calculateFormData.weekEnd
      );

      if (result.success && result.data) {
        toast.success(`Payout calculated: ${formatCurrency(Number(result.data.finalPayoutAmount))}`);
        setShowCalculateDialog(false);
        setCalculateFormData({
          assignmentId: "",
          weekStart: "",
          weekEnd: "",
          notes: "",
        });

        // Pre-fill process form with calculated data
        setProcessFormData({
          assignmentId: calculateFormData.assignmentId,
          weekStart: calculateFormData.weekStart,
          weekEnd: calculateFormData.weekEnd,
          payoutAmount: result.data.finalPayoutAmount,
          notes: calculateFormData.notes,
        });
        setShowProcessDialog(true);
      } else {
        toast.error(result.error || "Failed to calculate payout");
      }
    } catch (error) {
      console.error("Error calculating payout:", error);
      toast.error("Failed to calculate payout");
    }
  };

  const handleProcessPayout = async () => {
    if (!processFormData.assignmentId || !processFormData.weekStart || !processFormData.weekEnd || !processFormData.payoutAmount) {
      toast.error("Please fill in all required fields");
      return;
    }

    try {
      const formData = new FormData();
      formData.append("assignmentId", processFormData.assignmentId);
      formData.append("weekStart", processFormData.weekStart);
      formData.append("weekEnd", processFormData.weekEnd);
      formData.append("payoutAmount", processFormData.payoutAmount);
      formData.append("notes", processFormData.notes);

      const result = await processDriverPayoutAction(null, formData);

      if (result.success) {
        toast.success("Payout processed and sent for approval");
        setShowProcessDialog(false);
        setProcessFormData({
          assignmentId: "",
          weekStart: "",
          weekEnd: "",
          payoutAmount: "",
          notes: "",
        });
        loadPayoutData();
        loadPayoutStats();
        onRefresh?.();
      } else {
        toast.error(result.error || "Failed to process payout");
      }
    } catch (error) {
      console.error("Error processing payout:", error);
      toast.error("Failed to process payout");
    }
  };

  const handleApprovePayout = async () => {
    if (!selectedPayout || !approvalData.notes.trim()) {
      toast.error("Please provide approval notes");
      return;
    }

    try {
      const formData = new FormData();
      formData.append("payoutId", selectedPayout.id.toString());
      formData.append("notes", approvalData.notes);

      const result = approvalData.action === "approve" 
        ? await approveDriverPayoutAction(null, formData)
        : await rejectDriverPayoutAction(null, formData);

      if (result.success) {
        toast.success(`Payout ${approvalData.action}d successfully`);
        setShowApprovalDialog(false);
        setSelectedPayout(null);
        setApprovalData({ action: "approve", notes: "" });
        loadPayoutData();
        loadPayoutStats();
      } else {
        toast.error(result.error || `Failed to ${approvalData.action} payout`);
      }
    } catch (error) {
      console.error(`Error ${approvalData.action}ing payout:`, error);
      toast.error(`Failed to ${approvalData.action} payout`);
    }
  };

  const handleBulkProcess = async () => {
    if (!bulkFormData.weekStart || !bulkFormData.weekEnd || bulkFormData.assignmentPayouts.length === 0) {
      toast.error("Please provide week dates and at least one payout");
      return;
    }

    try {
      const formData = new FormData();
      formData.append("weekStart", bulkFormData.weekStart);
      formData.append("weekEnd", bulkFormData.weekEnd);
      formData.append("payoutsData", JSON.stringify(bulkFormData.assignmentPayouts));

      const result = await processBulkPayoutsAction(null, formData);

      if (result.success) {
        toast.success(`${result.data?.successful?.length || 0} payouts processed successfully`);
        if (result.data?.failed && result.data.failed.length > 0) {
          toast.warning(`${result.data.failed.length} payouts failed to process`);
        }
        setShowBulkDialog(false);
        setBulkFormData({
          weekStart: "",
          weekEnd: "",
          assignmentPayouts: [],
        });
        loadPayoutData();
        loadPayoutStats();
      } else {
        toast.error(result.error || "Failed to process bulk payouts");
      }
    } catch (error) {
      console.error("Error processing bulk payouts:", error);
      toast.error("Failed to process bulk payouts");
    }
  };

  const openApprovalDialog = (payout: PayoutRecord, action: "approve" | "reject") => {
    setSelectedPayout(payout);
    setApprovalData({ action, notes: "" });
    setShowApprovalDialog(true);
  };

  const openBulkDialog = () => {
    setBulkFormData({
      weekStart: "",
      weekEnd: "",
      assignmentPayouts: assignments.map(assignment => ({
        assignmentId: assignment.id,
        payoutAmount: "",
        notes: "",
      })),
    });
    setShowBulkDialog(true);
  };

  const handleRefreshPayouts = async () => {
    try {
      const result = await refreshPayoutsAction();
      
      if (result.success) {
        toast.success("Payout data refreshed successfully");
        loadPayoutData();
        loadPayoutStats();
      } else {
        toast.error("Failed to refresh payout data");
      }
    } catch (error) {
      console.error("Error refreshing payouts:", error);
      toast.error("Failed to refresh payout data");
    }
  };

  // Filter payouts based on search and filters
  const filteredPayouts = payouts.filter(payout => {
    const assignment = assignments.find(a => a.id === payout.assignmentId.toString());
    const matchesSearch = assignment?.driverName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         assignment?.vehicleName.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesStatus = filterStatus === "all" || payout.status === filterStatus;
    
    const matchesWeek = !selectedWeek || payout.periodStart.includes(selectedWeek);
    
    return matchesSearch && matchesStatus && matchesWeek;
  });

  const formatCurrency = (amount: number | string) => {
    const num = typeof amount === 'string' ? parseFloat(amount) : amount;
    return `R${num.toLocaleString()}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getPayoutStatusColor = (status: string) => {
    switch (status) {
      case "approved":
        return "bg-green-100 text-green-800 border-green-200";
      case "pending":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "rejected":
        return "bg-red-100 text-red-800 border-red-200";
      case "processing":
        return "bg-blue-100 text-blue-800 border-blue-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getPayoutStatusIcon = (status: string) => {
    switch (status) {
      case "approved":
        return <CheckCircle size={16} className="text-green-600" />;
      case "pending":
        return <Clock size={16} className="text-yellow-600" />;
      case "rejected":
        return <XCircle size={16} className="text-red-600" />;
      case "processing":
        return <RefreshCw size={16} className="text-blue-600" />;
      default:
        return <Clock size={16} className="text-gray-600" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold">Driver Payout Management</h2>
          <p className="text-sm text-gray-500">Process and manage driver payouts with approval workflows</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={() => setShowStatsDialog(true)} variant="outline">
            <Target size={16} className="mr-2" />
            View Stats
          </Button>
          <Button onClick={openBulkDialog} variant="outline">
            <Upload size={16} className="mr-2" />
            Bulk Process
          </Button>
          <Button onClick={() => setShowCalculateDialog(true)} variant="outline">
            <Calculator size={16} className="mr-2" />
            Calculate Payout
          </Button>
          <Button onClick={() => setShowProcessDialog(true)} className="bg-[#009639] hover:bg-[#007A2F]">
            <Send size={16} className="mr-2" />
            Process Payout
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-6 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <DollarSign className="h-8 w-8 text-[#009639]" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Payouts</p>
                <p className="text-2xl font-bold">{formatCurrency(payoutStats.totalPayouts)}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <Clock className="h-8 w-8 text-yellow-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Pending</p>
                <p className="text-2xl font-bold">{formatCurrency(payoutStats.pendingAmount)}</p>
                <p className="text-xs text-gray-500">({payoutStats.pendingCount} payouts)</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <CheckCircle className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Approved</p>
                <p className="text-2xl font-bold">{formatCurrency(payoutStats.approvedAmount)}</p>
                <p className="text-xs text-gray-500">({payoutStats.approvedCount} payouts)</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <XCircle className="h-8 w-8 text-red-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Rejected</p>
                <p className="text-2xl font-bold">{formatCurrency(payoutStats.rejectedAmount)}</p>
                <p className="text-xs text-gray-500">({payoutStats.rejectedCount} payouts)</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <Target className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Average Payout</p>
                <p className="text-2xl font-bold">{formatCurrency(payoutStats.averagePayout)}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <TrendingUp className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Highest Payout</p>
                <p className="text-2xl font-bold">{formatCurrency(payoutStats.highestPayout)}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search drivers..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-9 w-64"
            />
          </div>

          <Select value={filterStatus} onValueChange={setFilterStatus}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Payout Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="processing">Processing</SelectItem>
              <SelectItem value="approved">Approved</SelectItem>
              <SelectItem value="rejected">Rejected</SelectItem>
            </SelectContent>
          </Select>

          <Input
            type="date"
            value={selectedWeek}
            onChange={(e) => setSelectedWeek(e.target.value)}
            className="w-40"
            placeholder="Filter by week"
          />
        </div>

        <Button onClick={handleRefreshPayouts} variant="outline">
          <RefreshCw size={16} className="mr-2" />
          Refresh Data
        </Button>
      </div>

      {/* Payouts Table */}
      <Card>
        <CardHeader>
          <CardTitle>Driver Payout Records</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Driver</TableHead>
                <TableHead>Vehicle</TableHead>
                <TableHead>Week Period</TableHead>
                <TableHead>Payout Amount</TableHead>
                <TableHead>Net Earnings</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Processed Date</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={8} className="text-center py-8">
                    <div className="flex items-center justify-center">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#009639]"></div>
                      <span className="ml-2">Loading payouts...</span>
                    </div>
                  </TableCell>
                </TableRow>
              ) : filteredPayouts.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={8} className="text-center py-8">
                    <div className="text-gray-500">
                      <CreditCard size={48} className="mx-auto mb-4 text-gray-300" />
                      <p>No payout records found</p>
                      <p className="text-sm">Process your first driver payout to get started</p>
                    </div>
                  </TableCell>
                </TableRow>
              ) : (
                filteredPayouts.map((payout) => {
                  const assignment = assignments.find(a => a.id === payout.assignmentId.toString());
                  return (
                    <TableRow key={payout.id}>
                      <TableCell>
                        <div>
                          <p className="font-medium">{assignment?.driverName || "Unknown"}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <p className="font-medium">{assignment?.vehicleName || "Unknown"}</p>
                          <p className="text-sm text-gray-500">{assignment?.vehicleRegistration || ""}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <p className="font-medium">{formatDate(payout.periodStart)} - {formatDate(payout.periodEnd)}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <span className="font-bold text-green-600">
                          {formatCurrency(payout.payoutAmount)}
                        </span>
                      </TableCell>
                      <TableCell>
                        <span className="font-bold text-blue-600">
                          {formatCurrency(payout.netEarnings)}
                        </span>
                        {payout.totalDebtDeduction && parseFloat(payout.totalDebtDeduction) > 0 && (
                          <p className="text-xs text-orange-600">
                            Debt Deducted: {formatCurrency(payout.totalDebtDeduction)}
                          </p>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {getPayoutStatusIcon(payout.status)}
                          <Badge variant="outline" className={getPayoutStatusColor(payout.status)}>
                            {payout.status.charAt(0).toUpperCase() + payout.status.slice(1)}
                          </Badge>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          {payout.processedAt && (
                            <p className="font-medium">{formatDate(payout.processedAt)}</p>
                          )}
                          {payout.approvedAt && (
                            <p className="text-sm text-green-600">
                              Approved: {formatDate(payout.approvedAt)}
                            </p>
                          )}
                          {payout.status === "rejected" && (
                            <p className="text-sm text-red-600">
                              Status: Rejected
                            </p>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <Settings size={16} />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            {payout.status === "pending_approval" && (
                              <>
                                <DropdownMenuItem onClick={() => openApprovalDialog(payout, "approve")}>
                                  <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
                                  Approve Payout
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => openApprovalDialog(payout, "reject")}>
                                  <XCircle className="mr-2 h-4 w-4 text-red-600" />
                                  Reject Payout
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                              </>
                            )}
                            <DropdownMenuItem>
                              <Eye className="mr-2 h-4 w-4" />
                              View Details
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Download className="mr-2 h-4 w-4" />
                              Download Receipt
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  );
                })
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Calculate Payout Dialog */}
      <Dialog open={showCalculateDialog} onOpenChange={setShowCalculateDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Calculate Driver Payout</DialogTitle>
            <DialogDescription>
              Calculate payout amount based on earnings and debt deductions
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="calcAssignment">Assignment *</Label>
              <Select value={calculateFormData.assignmentId} onValueChange={(value) => 
                setCalculateFormData(prev => ({ ...prev, assignmentId: value }))
              }>
                <SelectTrigger>
                  <SelectValue placeholder="Select assignment" />
                </SelectTrigger>
                <SelectContent>
                  {assignments.map(assignment => (
                    <SelectItem key={assignment.id} value={assignment.id}>
                      {assignment.driverName} - {assignment.vehicleName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="calcWeekStart">Week Start *</Label>
                <Input
                  id="calcWeekStart"
                  type="date"
                  value={calculateFormData.weekStart}
                  onChange={(e) => setCalculateFormData(prev => ({ ...prev, weekStart: e.target.value }))}
                />
              </div>
              <div>
                <Label htmlFor="calcWeekEnd">Week End *</Label>
                <Input
                  id="calcWeekEnd"
                  type="date"
                  value={calculateFormData.weekEnd}
                  onChange={(e) => setCalculateFormData(prev => ({ ...prev, weekEnd: e.target.value }))}
                />
              </div>
            </div>
            <div>
              <Label htmlFor="calcNotes">Notes</Label>
              <Textarea
                id="calcNotes"
                placeholder="Additional notes for calculation..."
                value={calculateFormData.notes}
                onChange={(e) => setCalculateFormData(prev => ({ ...prev, notes: e.target.value }))}
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowCalculateDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleCalculatePayout} className="bg-[#009639] hover:bg-[#007A2F]">
              <Calculator size={16} className="mr-2" />
              Calculate Payout
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Process Payout Dialog */}
      <Dialog open={showProcessDialog} onOpenChange={setShowProcessDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Process Driver Payout</DialogTitle>
            <DialogDescription>
              Process payout for driver approval
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="procAssignment">Assignment *</Label>
              <Select value={processFormData.assignmentId} onValueChange={(value) => 
                setProcessFormData(prev => ({ ...prev, assignmentId: value }))
              }>
                <SelectTrigger>
                  <SelectValue placeholder="Select assignment" />
                </SelectTrigger>
                <SelectContent>
                  {assignments.map(assignment => (
                    <SelectItem key={assignment.id} value={assignment.id}>
                      {assignment.driverName} - {assignment.vehicleName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="procWeekStart">Week Start *</Label>
                <Input
                  id="procWeekStart"
                  type="date"
                  value={processFormData.weekStart}
                  onChange={(e) => setProcessFormData(prev => ({ ...prev, weekStart: e.target.value }))}
                />
              </div>
              <div>
                <Label htmlFor="procWeekEnd">Week End *</Label>
                <Input
                  id="procWeekEnd"
                  type="date"
                  value={processFormData.weekEnd}
                  onChange={(e) => setProcessFormData(prev => ({ ...prev, weekEnd: e.target.value }))}
                />
              </div>
            </div>
            <div>
              <Label htmlFor="payoutAmount">Payout Amount (R) *</Label>
              <Input
                id="payoutAmount"
                type="number"
                step="0.01"
                placeholder="0.00"
                value={processFormData.payoutAmount}
                onChange={(e) => setProcessFormData(prev => ({ ...prev, payoutAmount: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="procNotes">Notes</Label>
              <Textarea
                id="procNotes"
                placeholder="Additional notes for processing..."
                value={processFormData.notes}
                onChange={(e) => setProcessFormData(prev => ({ ...prev, notes: e.target.value }))}
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowProcessDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleProcessPayout} className="bg-[#009639] hover:bg-[#007A2F]">
              <Send size={16} className="mr-2" />
              Process Payout
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Bulk Process Dialog */}
      <Dialog open={showBulkDialog} onOpenChange={setShowBulkDialog}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Bulk Process Driver Payouts</DialogTitle>
            <DialogDescription>
              Process payouts for multiple drivers for the same week period
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="bulkWeekStart">Week Start *</Label>
                <Input
                  id="bulkWeekStart"
                  type="date"
                  value={bulkFormData.weekStart}
                  onChange={(e) => setBulkFormData(prev => ({ ...prev, weekStart: e.target.value }))}
                />
              </div>
              <div>
                <Label htmlFor="bulkWeekEnd">Week End *</Label>
                <Input
                  id="bulkWeekEnd"
                  type="date"
                  value={bulkFormData.weekEnd}
                  onChange={(e) => setBulkFormData(prev => ({ ...prev, weekEnd: e.target.value }))}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label>Driver Payouts</Label>
              <div className="max-h-60 overflow-y-auto border rounded p-4">
                {bulkFormData.assignmentPayouts.map((item, index) => {
                  const assignment = assignments.find(a => a.id === item.assignmentId);
                  return (
                    <div key={item.assignmentId} className="grid grid-cols-3 gap-4 mb-4 p-3 border rounded">
                      <div>
                        <Label className="text-sm">{assignment?.driverName}</Label>
                        <p className="text-xs text-gray-500">{assignment?.vehicleName}</p>
                      </div>
                      <div>
                        <Input
                          type="number"
                          step="0.01"
                          placeholder="Payout Amount (R)"
                          value={item.payoutAmount}
                          onChange={(e) => {
                            const updated = [...bulkFormData.assignmentPayouts];
                            updated[index].payoutAmount = e.target.value;
                            setBulkFormData(prev => ({ ...prev, assignmentPayouts: updated }));
                          }}
                        />
                      </div>
                      <div>
                        <Input
                          placeholder="Notes"
                          value={item.notes}
                          onChange={(e) => {
                            const updated = [...bulkFormData.assignmentPayouts];
                            updated[index].notes = e.target.value;
                            setBulkFormData(prev => ({ ...prev, assignmentPayouts: updated }));
                          }}
                        />
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowBulkDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleBulkProcess} className="bg-[#009639] hover:bg-[#007A2F]">
              Process All Payouts
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Approval Dialog */}
      <AlertDialog open={showApprovalDialog} onOpenChange={setShowApprovalDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {approvalData.action === "approve" ? "Approve" : "Reject"} Payout
            </AlertDialogTitle>
            <AlertDialogDescription>
              {approvalData.action === "approve" 
                ? "This will approve the payout and initiate payment to the driver."
                : "This will reject the payout. Please provide a reason."
              }
              {selectedPayout && (
                <div className="mt-2 p-3 bg-gray-50 rounded">
                  <p><strong>Driver:</strong> {assignments.find(a => a.id === selectedPayout.assignmentId.toString())?.driverName}</p>
                  <p><strong>Amount:</strong> {formatCurrency(selectedPayout.payoutAmount)}</p>
                  <p><strong>Week:</strong> {formatDate(selectedPayout.periodStart)} - {formatDate(selectedPayout.periodEnd)}</p>
                </div>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <div className="my-4">
            <Label htmlFor="approvalNotes">
              {approvalData.action === "approve" ? "Approval" : "Rejection"} Notes *
            </Label>
            <Textarea
              id="approvalNotes"
              placeholder={`Reason for ${approvalData.action}...`}
              value={approvalData.notes}
              onChange={(e) => setApprovalData(prev => ({ ...prev, notes: e.target.value }))}
              rows={3}
              className="mt-2"
            />
          </div>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleApprovePayout}
              className={approvalData.action === "approve" 
                ? "bg-green-600 hover:bg-green-700" 
                : "bg-red-600 hover:bg-red-700"
              }
              disabled={!approvalData.notes.trim()}
            >
              {approvalData.action === "approve" ? "Approve Payout" : "Reject Payout"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Stats Dialog */}
      <Dialog open={showStatsDialog} onOpenChange={setShowStatsDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Payout Statistics</DialogTitle>
            <DialogDescription>
              Detailed payout statistics and performance metrics
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center">
                    <Percent className="h-8 w-8 text-blue-600" />
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-500">Processing Rate</p>
                      <p className="text-xl font-bold">{payoutData.processingRate}%</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center">
                    <CheckCircle className="h-8 w-8 text-green-600" />
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-500">Approval Rate</p>
                      <p className="text-xl font-bold">{payoutData.approvalRate}%</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-medium">Status Distribution</h4>
              <div className="grid grid-cols-4 gap-2">
                {[
                  { status: "pending", count: payoutStats.pendingCount, color: "text-yellow-600" },
                  { status: "approved", count: payoutStats.approvedCount, color: "text-green-600" },
                  { status: "rejected", count: payoutStats.rejectedCount, color: "text-red-600" },
                  { status: "processed", count: payouts.filter(p => p.status === "processed").length, color: "text-blue-600" },
                ].map(({ status, count, color }) => {
                  const percentage = payouts.length > 0 ? ((count / payouts.length) * 100).toFixed(1) : "0";
                  return (
                    <div key={status} className="text-center p-2 border rounded">
                      <p className="text-xs uppercase font-medium">{status}</p>
                      <p className={`text-lg font-bold ${color}`}>{count}</p>
                      <p className="text-xs text-gray-500">{percentage}%</p>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button onClick={() => setShowStatsDialog(false)}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
} 